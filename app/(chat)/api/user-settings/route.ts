import { auth } from "@/app/(auth)/auth";
import { getUserSettingsByUserId, upsertUserSettings } from "@/lib/db/queries";
import { ChatSDKError } from "@/lib/errors";
import { z } from "zod";

const updateUserSettingsSchema = z.object({
  firstName: z.string().optional(),
  jobRole: z.string().optional(),
  jobDescription: z.string().optional(),
});

export async function GET() {
  try {
    const session = await auth();

    if (!session?.user) {
      return new ChatSDKError("unauthorized:api").toResponse();
    }

    const userSettings = await getUserSettingsByUserId({
      userId: session.user.id,
    });

    return Response.json(userSettings || null, { status: 200 });
  } catch (error) {
    return new ChatSDKError(
      "bad_request:database",
      "Failed to get user settings",
    ).toResponse();
  }
}

export async function PATCH(request: Request) {
  try {
    const session = await auth();

    if (!session?.user) {
      return new ChatSDKError("unauthorized:api").toResponse();
    }

    const body = await request.json();
    const validatedData = updateUserSettingsSchema.parse(body);

    const updatedSettings = await upsertUserSettings({
      userId: session.user.id,
      firstName: validatedData.firstName,
      jobRole: validatedData.jobRole,
      jobDescription: validatedData.jobDescription,
    });

    return Response.json(updatedSettings, { status: 200 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return new ChatSDKError(
        "bad_request:api",
        "Invalid user settings data",
      ).toResponse();
    }

    return new ChatSDKError(
      "bad_request:database",
      "Failed to update user settings",
    ).toResponse();
  }
}
